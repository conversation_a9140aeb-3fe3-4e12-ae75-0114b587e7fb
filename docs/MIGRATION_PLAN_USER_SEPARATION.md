# Migration Plan: Tách Riêng User Types

## 📋 Tổng Quan Migration

Kế hoạch migration từ cấu trúc **Users** hi<PERSON><PERSON> tại (chứa tất cả user types) sang cấu trúc tách riêng:
- **Users**: Admin và Staff only
- **Students**: Bảng riêng biệt
- **Lecturers**: Bảng riêng biệt

Đ<PERSON>m bảo tương thích với hệ thống campus-based roles đã có.

## 🎯 Mục Tiêu Migration

### ✅ Preserve Existing Data
- Giữ nguyên tất cả dữ liệu người dùng hiện tại
- Maintain campus role assignments
- Preserve student enrollment records
- Keep permission hierarchy intact

### ✅ Zero Downtime
- Gradual migration strategy
- Backward compatibility during transition
- Rollback capabilities
- Data integrity throughout process

### ✅ Enhanced Architecture
- Improved performance with smaller tables
- Better security isolation
- Cleaner separation of concerns
- Scalable for future growth

## 📊 Current vs Target Architecture

### Current Structure
```
users (current)
├── All user types (admin, staff, students, lecturers)
├── campus_user_roles → Role assignments per campus
├── student_enrollments → Academic records
└── student_unit_enrollments → Course registrations
```

### Target Structure
```
users (admins & staff only)
├── campus_user_roles → Role assignments per campus
└── teaching_assignments → Staff teaching roles

students (independent)
├── migrated_from_user_id → Track migration source
├── campus_student_roles → Student leadership roles
└── student_enrollments → Academic records

lecturers (independent)
├── campus_lecturer_roles → Admin roles for lecturers
├── teaching_assignments → Teaching assignments
└── advisor_students → Student supervision
```

## 🛠️ Migration Strategy

### Phase 1: Preparation (Week 1-2)

#### 1.1 Database Schema Updates
```sql
-- Add new fields to users table for staff functionality
ALTER TABLE users ADD COLUMN user_type ENUM('admin', 'staff', 'student', 'lecturer') DEFAULT 'staff';
ALTER TABLE users ADD COLUMN employee_id VARCHAR(20) UNIQUE;
ALTER TABLE users ADD COLUMN can_teach BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN max_teaching_hours DECIMAL(4,2) DEFAULT 0.00;
ALTER TABLE users ADD COLUMN department_id BIGINT UNSIGNED;
ALTER TABLE users ADD COLUMN position VARCHAR(255);
ALTER TABLE users ADD COLUMN hire_date DATE;
ALTER TABLE users ADD COLUMN employment_status ENUM('active', 'on_leave', 'terminated', 'retired') DEFAULT 'active';
ALTER TABLE users ADD COLUMN office_location VARCHAR(100);

-- Add foreign key for department
ALTER TABLE users ADD FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL;

-- Add indexes for performance
CREATE INDEX idx_users_type_status ON users(user_type, employment_status);
CREATE INDEX idx_users_employee_id ON users(employee_id);
```

#### 1.2 Create New Tables
```sql
-- Students table (independent)
CREATE TABLE students (
    id BIGINT UNSIGNED PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    middle_name VARCHAR(100),
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    
    -- Migration tracking
    migrated_from_user_id BIGINT UNSIGNED NULL,
    migration_completed_at TIMESTAMP NULL,
    
    -- Academic Information
    campus_id BIGINT UNSIGNED NOT NULL,
    program_id BIGINT UNSIGNED NOT NULL,
    specialization_id BIGINT UNSIGNED,
    admission_date DATE NOT NULL,
    expected_graduation_date DATE,
    student_type ENUM('full_time', 'part_time', 'exchange', 'visiting') DEFAULT 'full_time',
    enrollment_status ENUM('enrolled', 'active', 'on_leave', 'suspended', 'graduated', 'dropped_out') DEFAULT 'enrolled',
    
    -- Academic Progress
    current_year INTEGER DEFAULT 1,
    current_semester INTEGER DEFAULT 1,
    total_credits_earned DECIMAL(5,2) DEFAULT 0.00,
    cumulative_gpa DECIMAL(3,2),
    academic_standing ENUM('good_standing', 'probation', 'suspension', 'dean_list', 'honor_roll') DEFAULT 'good_standing',
    advisor_lecturer_id BIGINT UNSIGNED,
    
    -- Contact Information
    parent_guardian_name VARCHAR(255),
    parent_guardian_phone VARCHAR(20),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    
    -- System Fields
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    email_verified_at TIMESTAMP NULL,
    remember_token VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (migrated_from_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (campus_id) REFERENCES campuses(id) ON DELETE RESTRICT,
    FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE RESTRICT,
    FOREIGN KEY (specialization_id) REFERENCES specializations(id) ON DELETE SET NULL,
    FOREIGN KEY (advisor_lecturer_id) REFERENCES lecturers(id) ON DELETE SET NULL,
    
    INDEX idx_students_student_id (student_id),
    INDEX idx_students_email (email),
    INDEX idx_students_campus (campus_id),
    INDEX idx_students_migration (migrated_from_user_id)
);

-- Lecturers table (independent)
CREATE TABLE lecturers (
    id BIGINT UNSIGNED PRIMARY KEY,
    lecturer_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    middle_name VARCHAR(100),
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    
    -- Basic Information
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    nationality VARCHAR(100) DEFAULT 'Vietnamese',
    national_id VARCHAR(20) UNIQUE,
    address TEXT,
    avatar_url VARCHAR(500),
    
    -- Employment Information
    campus_id BIGINT UNSIGNED NOT NULL,
    department_id BIGINT UNSIGNED,
    hire_date DATE NOT NULL,
    employment_type ENUM('full_time', 'part_time', 'adjunct', 'visiting', 'emeritus') DEFAULT 'full_time',
    employment_status ENUM('active', 'on_leave', 'sabbatical', 'retired', 'terminated') DEFAULT 'active',
    
    -- Academic Information
    academic_rank ENUM('instructor', 'assistant_professor', 'associate_professor', 'professor', 'distinguished_professor') DEFAULT 'instructor',
    highest_degree VARCHAR(100),
    highest_degree_institution VARCHAR(255),
    highest_degree_year YEAR,
    areas_of_expertise JSON,
    research_interests TEXT,
    
    -- Office & Contact
    office_location VARCHAR(100),
    office_hours TEXT,
    cv_file_path VARCHAR(500),
    
    -- Teaching Load Management
    teaching_load_hours DECIMAL(4,2) DEFAULT 0.00,
    max_teaching_load_hours DECIMAL(4,2) DEFAULT 40.00,
    is_thesis_supervisor BOOLEAN DEFAULT FALSE,
    max_thesis_students INTEGER DEFAULT 5,
    current_thesis_students INTEGER DEFAULT 0,
    
    -- System Fields
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    email_verified_at TIMESTAMP NULL,
    remember_token VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (campus_id) REFERENCES campuses(id) ON DELETE RESTRICT,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    
    INDEX idx_lecturers_lecturer_id (lecturer_id),
    INDEX idx_lecturers_email (email),
    INDEX idx_lecturers_campus (campus_id)
);
```

#### 1.3 Extended Role Tables
```sql
-- Campus Student Roles (for student leadership)
CREATE TABLE campus_student_roles (
    id BIGINT UNSIGNED PRIMARY KEY,
    student_id BIGINT UNSIGNED NOT NULL,
    campus_id BIGINT UNSIGNED NOT NULL,
    role_id BIGINT UNSIGNED NOT NULL,
    assigned_date DATE DEFAULT (CURRENT_DATE),
    assigned_by BIGINT UNSIGNED,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (campus_id) REFERENCES campuses(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_student_campus_role (student_id, campus_id, role_id)
);

-- Campus Lecturer Roles (for lecturer admin roles)
CREATE TABLE campus_lecturer_roles (
    id BIGINT UNSIGNED PRIMARY KEY,
    lecturer_id BIGINT UNSIGNED NOT NULL,
    campus_id BIGINT UNSIGNED NOT NULL,
    role_id BIGINT UNSIGNED NOT NULL,
    assigned_date DATE DEFAULT (CURRENT_DATE),
    assigned_by BIGINT UNSIGNED,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (lecturer_id) REFERENCES lecturers(id) ON DELETE CASCADE,
    FOREIGN KEY (campus_id) REFERENCES campuses(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_lecturer_campus_role (lecturer_id, campus_id, role_id)
);

-- Teaching Assignments
CREATE TABLE teaching_assignments (
    id BIGINT UNSIGNED PRIMARY KEY,
    semester_unit_offering_id BIGINT UNSIGNED NOT NULL,
    
    -- Either lecturer OR staff can be assigned
    lecturer_id BIGINT UNSIGNED NULL,
    user_id BIGINT UNSIGNED NULL,      -- For staff teaching
    
    assignment_type ENUM('primary', 'co_instructor', 'teaching_assistant', 'grader') DEFAULT 'primary',
    teaching_percentage DECIMAL(5,2) DEFAULT 100.00,
    credit_hours DECIMAL(3,1) NOT NULL,
    hourly_rate DECIMAL(8,2),
    total_compensation DECIMAL(10,2),
    
    assigned_date DATE NOT NULL,
    assignment_status ENUM('pending', 'accepted', 'declined', 'active', 'completed') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (semester_unit_offering_id) REFERENCES semester_unit_offerings(id) ON DELETE CASCADE,
    FOREIGN KEY (lecturer_id) REFERENCES lecturers(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Ensure either lecturer_id OR user_id is set, not both
    CHECK ((lecturer_id IS NOT NULL AND user_id IS NULL) OR (lecturer_id IS NULL AND user_id IS NOT NULL))
);
```

### Phase 2: Data Classification (Week 3)

#### 2.1 Identify Current User Types
```sql
-- Analyze current users
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN EXISTS(SELECT 1 FROM student_enrollments se WHERE se.user_id = users.id) THEN 1 END) as students,
    COUNT(CASE WHEN email LIKE '%@admin.%' OR email LIKE '%@staff.%' THEN 1 END) as potential_staff,
    COUNT(CASE WHEN email LIKE '%@lecturer.%' OR email LIKE '%@faculty.%' THEN 1 END) as potential_lecturers
FROM users;

-- Update user_type based on existing data
UPDATE users 
SET user_type = 'student' 
WHERE EXISTS(SELECT 1 FROM student_enrollments se WHERE se.user_id = users.id);

UPDATE users 
SET user_type = 'admin' 
WHERE email LIKE '%@admin.%' OR name LIKE '%Admin%';

UPDATE users 
SET user_type = 'lecturer' 
WHERE email LIKE '%@lecturer.%' OR email LIKE '%@faculty.%';

UPDATE users 
SET user_type = 'staff' 
WHERE user_type IS NULL;
```

#### 2.2 Generate IDs for Migration
```php
// Artisan command to generate IDs
class GenerateMigrationIds extends Command
{
    public function handle()
    {
        // Generate employee IDs for admin/staff
        User::whereIn('user_type', ['admin', 'staff'])
            ->whereNull('employee_id')
            ->chunk(100, function ($users) {
                foreach ($users as $user) {
                    $user->update([
                        'employee_id' => $this->generateEmployeeId($user->user_type)
                    ]);
                }
            });
            
        $this->info('Employee IDs generated successfully');
    }
    
    private function generateEmployeeId(string $type): string
    {
        $prefix = $type === 'admin' ? 'ADM' : 'STF';
        $number = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        
        while (User::where('employee_id', $prefix . $number)->exists()) {
            $number = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        }
        
        return $prefix . $number;
    }
}
```

### Phase 3: Student Migration (Week 4-5)

#### 3.1 Create Student Records
```php
class MigrateStudentsCommand extends Command
{
    public function handle()
    {
        $studentUsers = User::where('user_type', 'student')
            ->with(['studentEnrollments.program', 'campusRoles'])
            ->get();
            
        DB::transaction(function () use ($studentUsers) {
            foreach ($studentUsers as $user) {
                $enrollment = $user->studentEnrollments->first();
                $campusId = $enrollment?->program?->campus_id ?? 1;
                
                // Create student record
                $student = Student::create([
                    'migrated_from_user_id' => $user->id,
                    'student_id' => $this->generateStudentId($campusId),
                    'first_name' => $this->extractFirstName($user->name),
                    'last_name' => $this->extractLastName($user->name),
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'password' => $user->password,
                    'campus_id' => $campusId,
                    'program_id' => $enrollment?->program_id ?? 1,
                    'specialization_id' => $enrollment?->specialization_id,
                    'enrollment_status' => $enrollment?->enrollment_status ?? 'enrolled',
                    'total_credits_earned' => $enrollment?->total_credit_hours ?? 0,
                    'cumulative_gpa' => $enrollment?->gpa_cumulative,
                    'academic_standing' => $this->mapAcademicStanding($enrollment),
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                ]);
                
                // Migrate campus roles to student roles
                foreach ($user->campusRoles as $campusRole) {
                    CampusStudentRole::create([
                        'student_id' => $student->id,
                        'campus_id' => $campusRole->campus_id,
                        'role_id' => $campusRole->role_id,
                        'assigned_date' => $campusRole->created_at,
                        'is_active' => true,
                    ]);
                }
                
                // Update enrollment references
                StudentEnrollment::where('user_id', $user->id)
                    ->update(['student_id' => $student->id]);
                    
                // Mark migration completed
                $user->update(['migration_completed_at' => now()]);
                
                $this->info("Migrated student: {$user->email} -> {$student->student_id}");
            }
        });
    }
    
    private function generateStudentId(int $campusId): string
    {
        $campusCode = Campus::find($campusId)->code ?? 'UNI';
        $year = date('y');
        $sequence = Student::where('student_id', 'like', "{$campusCode}{$year}%")->count() + 1;
        
        return $campusCode . $year . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
```

#### 3.2 Update Student Enrollment References
```php
// Add student_id column to student_enrollments
ALTER TABLE student_enrollments ADD COLUMN student_id BIGINT UNSIGNED NULL;
ALTER TABLE student_enrollments ADD FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE;

// Update references (handled in migration command above)
// Eventually drop user_id column after verification
```

### Phase 4: Lecturer Migration (Week 6)

#### 4.1 Create Lecturer Records
```php
class MigrateLecturersCommand extends Command
{
    public function handle()
    {
        $lecturerUsers = User::where('user_type', 'lecturer')
            ->with('campusRoles')
            ->get();
            
        DB::transaction(function () use ($lecturerUsers) {
            foreach ($lecturerUsers as $user) {
                $campusId = $user->campusRoles->first()?->campus_id ?? 1;
                
                $lecturer = Lecturer::create([
                    'lecturer_id' => $this->generateLecturerId($campusId),
                    'first_name' => $this->extractFirstName($user->name),
                    'last_name' => $this->extractLastName($user->name),
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'password' => $user->password,
                    'campus_id' => $campusId,
                    'hire_date' => $user->created_at->toDateString(),
                    'employment_type' => 'full_time',
                    'employment_status' => 'active',
                    'academic_rank' => 'instructor',
                    'max_teaching_load_hours' => 40.00,
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                ]);
                
                // Migrate campus roles
                foreach ($user->campusRoles as $campusRole) {
                    CampusLecturerRole::create([
                        'lecturer_id' => $lecturer->id,
                        'campus_id' => $campusRole->campus_id,
                        'role_id' => $campusRole->role_id,
                        'assigned_date' => $campusRole->created_at,
                        'is_active' => true,
                    ]);
                }
                
                $this->info("Migrated lecturer: {$user->email} -> {$lecturer->lecturer_id}");
            }
        });
    }
}
```

### Phase 5: Authentication Update (Week 7)

#### 5.1 Multi-Guard Configuration
```php
// config/auth.php
'guards' => [
    'web' => [
        'driver' => 'session',
        'provider' => 'users',
    ],
    'user' => [
        'driver' => 'session',
        'provider' => 'users',
    ],
    'student' => [
        'driver' => 'session',
        'provider' => 'students',
    ],
    'lecturer' => [
        'driver' => 'session',
        'provider' => 'lecturers',
    ],
],

'providers' => [
    'users' => [
        'driver' => 'eloquent',
        'model' => App\Models\User::class,
    ],
    'students' => [
        'driver' => 'eloquent',
        'model' => App\Models\Student::class,
    ],
    'lecturers' => [
        'driver' => 'eloquent',
        'model' => App\Models\Lecturer::class,
    ],
],
```

#### 5.2 Multi-Auth Middleware
```php
class CheckUserType
{
    public function handle($request, Closure $next, $userType)
    {
        $user = auth()->user();
        
        if (!$user) {
            return redirect()->route('login');
        }
        
        // Handle migration period
        if ($user instanceof User && $user->user_type === $userType) {
            return $next($request);
        }
        
        // Handle new separate tables
        if (($userType === 'student' && $user instanceof Student) ||
            ($userType === 'lecturer' && $user instanceof Lecturer) ||
            (in_array($userType, ['admin', 'staff']) && $user instanceof User)) {
            return $next($request);
        }
        
        abort(403, 'Unauthorized user type');
    }
}
```

### Phase 6: Cleanup (Week 8)

#### 6.1 Remove Migrated Users
```php
class CleanupMigratedUsersCommand extends Command
{
    public function handle()
    {
        // Only remove users that have been successfully migrated
        $migratedStudents = User::where('user_type', 'student')
            ->whereNotNull('migration_completed_at')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                      ->from('students')
                      ->whereColumn('students.migrated_from_user_id', 'users.id');
            });
            
        $count = $migratedStudents->count();
        $migratedStudents->delete();
        
        $this->info("Cleaned up {$count} migrated student users");
        
        // Similar cleanup for lecturers...
    }
}
```

#### 6.2 Update Foreign Key References
```sql
-- Remove user_id from student_enrollments after verification
ALTER TABLE student_enrollments DROP FOREIGN KEY student_enrollments_user_id_foreign;
ALTER TABLE student_enrollments DROP COLUMN user_id;

-- Update any other tables that reference users for students/lecturers
```

## 🧪 Testing Strategy

### Unit Tests
```php
class StudentMigrationTest extends TestCase
{
    public function test_student_migration_preserves_data()
    {
        $user = User::factory()->create(['user_type' => 'student']);
        $enrollment = StudentEnrollment::factory()->create(['user_id' => $user->id]);
        
        Artisan::call('migrate:students');
        
        $student = Student::where('migrated_from_user_id', $user->id)->first();
        
        $this->assertNotNull($student);
        $this->assertEquals($user->email, $student->email);
        $this->assertEquals($enrollment->program_id, $student->program_id);
    }
}
```

### Integration Tests
```php
class AuthenticationTest extends TestCase
{
    public function test_multi_guard_authentication()
    {
        $student = Student::factory()->create();
        
        $response = $this->post('/student/login', [
            'email' => $student->email,
            'password' => 'password',
        ]);
        
        $this->assertAuthenticatedAs($student, 'student');
    }
}
```

## 📊 Monitoring & Rollback

### Migration Monitoring
```php
class MigrationMonitorCommand extends Command
{
    public function handle()
    {
        $totalUsers = User::count();
        $migratedStudents = Student::whereNotNull('migrated_from_user_id')->count();
        $migratedLecturers = Lecturer::count();
        $remainingUsers = User::whereIn('user_type', ['admin', 'staff'])->count();
        
        $this->table(['Metric', 'Count'], [
            ['Total Original Users', $totalUsers],
            ['Migrated Students', $migratedStudents],
            ['Migrated Lecturers', $migratedLecturers],
            ['Remaining Users (Admin/Staff)', $remainingUsers],
            ['Migration Progress', round(($migratedStudents + $migratedLecturers) / $totalUsers * 100, 2) . '%'],
        ]);
    }
}
```

### Rollback Strategy
```php
class RollbackMigrationCommand extends Command
{
    public function handle()
    {
        if (!$this->confirm('Are you sure you want to rollback the migration?')) {
            return;
        }
        
        DB::transaction(function () {
            // Restore student users
            Student::whereNotNull('migrated_from_user_id')
                ->with('migratedFromUser')
                ->chunk(100, function ($students) {
                    foreach ($students as $student) {
                        if ($student->migratedFromUser) {
                            // Restore original user record
                            $student->migratedFromUser->restore();
                            
                            // Update enrollments back to user reference
                            StudentEnrollment::where('student_id', $student->id)
                                ->update(['user_id' => $student->migrated_from_user_id]);
                        }
                    }
                });
                
            // Drop new tables
            Schema::dropIfExists('students');
            Schema::dropIfExists('lecturers');
            Schema::dropIfExists('campus_student_roles');
            Schema::dropIfExists('campus_lecturer_roles');
            Schema::dropIfExists('teaching_assignments');
        });
        
        $this->info('Migration rollback completed');
    }
}
```

## 📈 Success Metrics

### Key Performance Indicators
- **Data Integrity**: 100% data preservation during migration
- **Authentication Success**: >99% login success rate post-migration
- **Performance Improvement**: 20-30% query performance improvement
- **Zero Downtime**: No service interruption during migration
- **User Experience**: Seamless transition for end users

### Verification Checklist
- [ ] All student data migrated successfully
- [ ] All lecturer data migrated successfully  
- [ ] Campus role assignments preserved
- [ ] Permission system functional
- [ ] Authentication working for all user types
- [ ] Student enrollment system operational
- [ ] Teaching assignment system functional
- [ ] Performance improvements verified
- [ ] Rollback procedures tested
- [ ] User acceptance testing completed

Kế hoạch migration này đảm bảo chuyển đổi an toàn và hiệu quả từ cấu trúc monolithic sang kiến trúc tách biệt, tận dụng tối đa infrastructure hiện có! 
